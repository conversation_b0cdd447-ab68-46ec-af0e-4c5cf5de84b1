from typing import Literal, List, Dict, Optional, Any
from typing_extensions import TypedDict
from pydantic import BaseModel, Field
from langgraph.graph import MessagesState
import uuid
import datetime

# Define the status literals for plans and steps
PlanStatus = Literal["active", "completed", "failed", "paused"]
StepStatus = Literal["pending", "in_progress", "completed", "failed", "skipped"]


class PlanStep(BaseModel):
    """Represents a single, atomic unit of work within the overall plan."""

    step_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Unique identifier for this step.",
    )
    step_index: int = Field(
        description="The 0-based position of the step in the plan's sequence."
    )
    agent_name: str = Field(
        description="Specifies which agent is responsible for executing this step."
    )
    title: str = Field(description="A concise summary of the step's objective.")
    description: str = Field(
        description="A detailed explanation of what needs to be done in this step."
    )
    requires_evaluation: bool = Field(
        default=False,
        description="If true, this step will be sent to the evaluator after execution.",
    )
    status: StepStatus = Field(
        default="pending", description="Tracks the current state of the step."
    )
    results: Optional[Dict] = Field(
        default_factory=dict,
        description="A flexible field to store any outputs, summaries, or key findings.",
    )
    error_message: Optional[str] = Field(
        default=None, description="Captures error details if the step fails."
    )
    error_report: Optional[Dict] = Field(
        default=None,
        description="Structured error report with category, severity, and recovery actions.",
    )
    retry_count: int = Field(
        default=0, description="Tracks how many times a step has been attempted."
    )
    dependencies: List[int] = Field(
        default_factory=list,
        description="List of step indices that must complete before this step.",
    )
    todo_id: Optional[str] = Field(
        default=None, description="ID of the corresponding todo item in todo-md-mcp."
    )
    filtered_tools: Optional[str] = Field(
        default=None,
        description="Tools filtered by ToolRetriever for this specific step.",
    )


class StructuredPlan(BaseModel):
    """Encapsulates the entire analysis workflow as a 'living plan'."""

    plan_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="A unique identifier for the entire plan.",
    )
    title: str = Field(description="The overall title of the analysis plan.")
    thought: str = Field(
        description="The high-level reasoning or strategy behind the plan."
    )
    steps: List[PlanStep] = Field(
        description="The ordered list of PlanStep objects that constitute the workflow."
    )
    status: PlanStatus = Field(
        default="active", description="The overall status of the plan."
    )
    current_step_index: int = Field(
        default=0,
        description="The index of the step currently being executed or evaluated.",
    )
    start_time: Optional[datetime.datetime] = Field(
        default=None, description="The timestamp when the plan execution started."
    )
    end_time: Optional[datetime.datetime] = Field(
        default=None, description="The timestamp when the plan execution finished."
    )

    def get_current_step(self) -> Optional[PlanStep]:
        """Get the current step to execute."""
        if 0 <= self.current_step_index < len(self.steps):
            return self.steps[self.current_step_index]
        return None

    def advance_to_next_step(self):
        """Finds the next step with 'pending' status and sets it as current."""
        # Start searching from the current step index + 1
        for i in range(self.current_step_index + 1, len(self.steps)):
            if self.steps[i].status == "pending":
                self.current_step_index = i
                return

        # If no pending steps are found after the current one, search from the beginning
        for i in range(self.current_step_index):
            if self.steps[i].status == "pending":
                self.current_step_index = i
                return

        # If still no pending steps, the plan is complete
        self.status = "completed"
        self.current_step_index = -1  # Sentinel for completion


class State(MessagesState):
    """The central state of the multi-agent system, holding the 'living plan'."""

    structured_plan: Optional[StructuredPlan] = Field(
        default=None, description="The dynamic plan that guides the workflow."
    )
    # The 'messages' attribute is inherited from MessagesState and will store the conversation history.
