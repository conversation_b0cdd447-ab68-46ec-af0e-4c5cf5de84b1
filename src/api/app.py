"""
src/api/app.py  –  LangGraph × AG-UI × CopilotKit bridge
========================================================
[… docstring unchanged …]
"""

from __future__ import annotations
import asyncio, json, uuid, logging
from datetime import datetime, UTC
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional, AsyncGenerator

from fastapi import FastAPI, Request, HTTPException, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# ─── import your LangGraph helpers ──────────────────────────────────────
from src.service.workflow_service import (
    initialize_graph,
    run_agent_workflow,  # type: ignore
    handle_user_confirmation,
)
from src.service.persistence_service import (
    list_conversation_threads,
    list_checkpoints_for_thread,
    get_conversation_history,
    resume_from_checkpoint,
)

# ─── import new message types for structured communication ───────────────
from src.graph.messages import (
    FinalAnswerMessage,
    AgentStepMessage,
    ToolOutputMessage,
    ToDoListUpdateMessage,
)

# ─── logging & FastAPI ---------------------------------------------------
logging.basicConfig(level=logging.INFO)
# Suppress specific warnings
logging.getLogger("langchain_google_genai._function_utils").setLevel(logging.ERROR)
log = logging.getLogger(__name__)


# Custom JSON encoder for datetime objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, datetime):
            return o.isoformat()
        return json.JSONEncoder.default(self, o)


@asynccontextmanager
async def lifespan(app: FastAPI):
    log.info("⏳  Initialising LangGraph …")
    await initialize_graph()
    app.state.tasks = {}
    yield


app = FastAPI(title="LangGraph × AG-UI bridge", lifespan=lifespan)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
    allow_credentials=True,
)


# ─── pydantic models -----------------------------------------------------
class Message(BaseModel):
    id: str
    role: str
    content: str


class RunInput(BaseModel):
    thread_id: str
    run_id: Optional[str] = None
    messages: List[Message]
    state: Optional[Dict[str, Any]] = None


class ConfirmationInput(BaseModel):
    thread_id: str
    user_response: str


@app.post("/agent")
async def agent(request: str = Form(...), files: List[UploadFile] = File(...)):
    thread_id = str(uuid.uuid4())

    # Persist files to disk
    # (implementation depends on your storage strategy)

    # For now, we'll just pass the filenames to the workflow
    # In a real app, you'd save them and pass paths or IDs
    file_info = [f.filename for f in files]

    # Create a message list for the workflow
    messages = [
        Message(
            id=str(uuid.uuid4()),
            role="user",
            content=f"{request}\n\nFiles: {', '.join(file_info)}",
        )
    ]

    # Use a background task to run the workflow
    # This allows us to return a task ID immediately
    task_id = str(uuid.uuid4())

    async def run_workflow_in_background():
        try:
            async for _ in _stream(thread_id, messages, task_id, {}):
                pass
        except Exception as e:
            log.error(f"Error in background task {task_id}: {e}")

    asyncio.create_task(run_workflow_in_background())

    return {"task_id": task_id}


# ─── AG-UI helpers -------------------------------------------------------
class AGUIEvent:
    """
    Wraps an event so it matches the AG-UI spec (v0.4, May-2025).

    • Converts legacy dot-style names → official ENUM strings.
    • Converts snake_case keys → camelCase keys.
    • Emits a Server-Sent-Event line usable by the JS / TS SDKs.
    """

    _TYPE_MAP = {
        "run.started": "RUN_STARTED",
        "run.finished": "RUN_FINISHED",
        "run.cancelled": "RUN_CANCELLED",
        "run.error": "RUN_ERROR",
        "step.started": "STEP_STARTED",
        "step.finished": "STEP_FINISHED",
        "text_message.start": "TEXT_MESSAGE_START",
        "text_message.content": "TEXT_MESSAGE_CONTENT",
        "text_message.end": "TEXT_MESSAGE_END",
        "tool_call.start": "TOOL_CALL_START",
        "tool_call.args": "TOOL_CALL_ARGS",
        "tool_call.end": "TOOL_CALL_END",
        "tool_call.result": "TOOL_CALL_RESULT",
        "state.snapshot": "STATE_SNAPSHOT",
        "state.delta": "STATE_DELTA",
        "messages.snapshot": "MESSAGES_SNAPSHOT",
    }

    _KEY_MAP = {
        "run_id": "runId",
        "thread_id": "threadId",
        "message_id": "messageId",
        "started_at": "startedAt",
        "finished_at": "finishedAt",
        "step_name": "stepName",
        "tool_call_id": "toolCallId",
        "tool_call_name": "toolCallName",
        "parent_message_id": "parentMessageId",
    }

    def __init__(self, typ: str, **d):
        self.type: str = self._TYPE_MAP.get(typ, typ)
        self.data: Dict[str, Any] = {self._KEY_MAP.get(k, k): v for k, v in d.items()}

    # Server-Sent-Event format
    def sse(self) -> str:
        return f"data: {json.dumps({'type': self.type, **self.data}, cls=DateTimeEncoder)}\n\n"


# ─── assistant / agent stub ---------------------------------------------
def _agent(now: str) -> Dict[str, Any]:
    return {
        "object": "assistant",
        "id": "multi-agent-workflow",
        "assistant_id": "multi-agent-workflow",
        "graph_id": "multi-agent-workflow",
        "name": "Multi-Agent Workflow",
        "description": "LangGraph multi-agent system",
        "version": 1,
        "config": {},
        "created_at": now,
        "updated_at": now,
    }


def _list_payload() -> Dict[str, Any]:
    now = datetime.now(UTC).isoformat()
    agent = _agent(now)
    return {
        "object": "list",
        "data": [agent],  # CopilotKit ≥0.7
        "assistants": [agent],  # LangGraph SDKs
        "agents": [agent],  # future-proof
        "has_more": False,
    }


def _root_payload() -> Dict[str, Any]:
    now = datetime.now(UTC).isoformat()
    agent = _agent(now)
    return {
        "sdkVersion": "ag-ui-bridge 0.1.0",
        "agents": [agent],
        "data": [agent],  # CopilotKit probes POST /
        "actions": [],
    }


# ─── register discovery routes (GET+POST) -------------------------------
def _reg(path: str, payload_fn):
    for m in ("GET", "POST"):
        app.add_api_route(path, lambda pf=payload_fn: pf(), methods=[m])
        app.add_api_route("/awp" + path, lambda pf=payload_fn: pf(), methods=[m])


for p, fn in [
    ("/", _root_payload),
    ("/info", _root_payload),
    ("/assistants", _list_payload),
    ("/assistants/search", _list_payload),
    ("/agents", _list_payload),
    ("/agents/search", _list_payload),
]:
    _reg(p, fn)


@app.get("/assistants/{assistant_id}")
@app.get("/agents/{assistant_id}")
def assistant_detail(assistant_id: str):
    if assistant_id != "multi-agent-workflow":
        raise HTTPException(404, "assistant not found")
    return _agent(datetime.now(UTC).isoformat())


# ─── AG-UI streaming endpoint ------------------------------------------
async def _stream(
    thread_id: str,
    msgs: List[Message],
    run_id: Optional[str],
    state: Dict[str, Any],
) -> AsyncGenerator[str, None]:
    run_id = run_id or str(uuid.uuid4())

    # run.started
    yield AGUIEvent(
        "run.started",
        run_id=run_id,
        thread_id=thread_id,
        started_at=datetime.now(UTC).isoformat(),
    ).sse()

    cur_msg_id: Optional[str] = None
    tool_metadata = {}  # Store tool inputs and metadata
    try:
        # state management events
        yield AGUIEvent(
            "state.snapshot",
            snapshot=state,
        ).sse()
        yield AGUIEvent(
            "messages.snapshot",
            messages=[m.dict() for m in msgs],
        ).sse()

        # Agent step names for better UX
        step_names = {
            "planner": "📋 Planning Analysis Strategy",
            "researcher": "🔍 Research & Data Discovery",
            "coder": "💻 Implementation & Execution",
            "browser": "🌐 Web Data Collection",
            "reporter": "📊 Results & Report Generation",
            "execution_evaluator": "✅ Quality Validation",
        }

        async for event in run_agent_workflow(
            msgs[-1].content,
            thread_id,
        ):
            if "event" in event:
                typ = event["event"]
                data = event.get("data", {})

                # Check for plan updates after planner completes
                if typ == "end_of_agent" and data.get("agent_name") == "planner":
                    # Try to get the current state to extract the plan
                    try:
                        from src.service.workflow_service import get_graph

                        current_graph = get_graph()
                        if current_graph:
                            config = {"configurable": {"thread_id": thread_id}}
                            current_state = await current_graph.aget_state(config)
                            if current_state and current_state.values.get(
                                "structured_plan"
                            ):
                                plan = current_state.values["structured_plan"]
                                if hasattr(plan, "dict"):
                                    plan_dict = plan.dict()
                                elif hasattr(plan, "model_dump"):
                                    plan_dict = plan.model_dump()
                                else:
                                    plan_dict = plan

                                yield AGUIEvent(
                                    "state.snapshot",
                                    snapshot={"structured_plan": plan_dict},
                                ).sse()
                    except Exception as e:
                        log.warning(f"Could not extract plan state: {e}")
                        pass

                # Agent lifecycle -> Step lifecycle
                if typ == "start_of_agent":
                    agent_name = data.get("agent_name")
                    if agent_name:
                        step_name = step_names.get(
                            agent_name, f"🤖 {agent_name.title()}"
                        )
                        yield AGUIEvent(
                            "step.started",
                            step_name=step_name,
                            agent_name=agent_name,
                            agent_id=data.get("agent_id"),
                        ).sse()

                elif typ == "end_of_agent":
                    agent_name = data.get("agent_name")
                    if agent_name:
                        step_name = step_names.get(
                            agent_name, f"🤖 {agent_name.title()}"
                        )
                        yield AGUIEvent(
                            "step.finished",
                            step_name=step_name,
                            agent_name=agent_name,
                            agent_id=data.get("agent_id"),
                        ).sse()
                        # End any active message when agent finishes
                        if cur_msg_id:
                            yield AGUIEvent(
                                "text_message.end",
                                message_id=cur_msg_id,
                            ).sse()
                            cur_msg_id = None

                # Tool calls from workflow service
                elif typ == "tool_call":
                    tool_name = data.get("tool_name")
                    tool_call_id = data.get("tool_call_id")
                    if "todo" in tool_name:
                        tool_metadata[tool_call_id] = data.get("tool_input", {})
                    yield AGUIEvent(
                        "tool_call.start",
                        tool_call_id=tool_call_id,
                        tool_call_name=tool_name,
                        parent_message_id=cur_msg_id,
                    ).sse()
                    yield AGUIEvent(
                        "tool_call.args",
                        tool_call_id=tool_call_id,
                        delta=json.dumps(data.get("tool_input", {})),
                    ).sse()

                elif typ == "tool_call_result":
                    tool_call_id = data.get("tool_call_id")
                    tool_result = data.get("tool_result", "")
                    yield AGUIEvent(
                        "tool_call.end",
                        tool_call_id=tool_call_id,
                    ).sse()
                    yield AGUIEvent(
                        "tool_call.result",
                        message_id=str(uuid.uuid4()),
                        tool_call_id=tool_call_id,
                        content=tool_result,
                        role="tool",
                    ).sse()

                # LLM streaming -> Text message streaming
                elif typ == "on_chat_model_stream":
                    delta = data.get("chunk", {}).get("content", "")
                    if delta:
                        if cur_msg_id is None:
                            cur_msg_id = str(uuid.uuid4())
                            yield AGUIEvent(
                                "text_message.start",
                                message_id=cur_msg_id,
                                role="assistant",
                            ).sse()
                        yield AGUIEvent(
                            "text_message.content",
                            message_id=cur_msg_id,
                            delta=delta,
                        ).sse()

                # Tool calls
                elif typ == "on_tool_start":
                    yield AGUIEvent(
                        "tool_call.start",
                        tool_call_id=data.get("run_id"),
                        tool_call_name=data.get("name"),
                        parent_message_id=cur_msg_id,
                    ).sse()
                    yield AGUIEvent(
                        "tool_call.args",
                        tool_call_id=data.get("run_id"),
                        delta=json.dumps(data.get("input", {})),
                    ).sse()
                elif typ == "on_tool_end":
                    yield AGUIEvent(
                        "tool_call.end",
                        tool_call_id=data.get("run_id"),
                    ).sse()
                    yield AGUIEvent(
                        "tool_call.result",
                        message_id=str(uuid.uuid4()),
                        tool_call_id=data.get("run_id"),
                        content=json.dumps(data.get("output", {})),
                        role="tool",
                    ).sse()

                # Handle custom message events (for direct agent communication)
                elif typ == "message":
                    message_data = data.get("delta", {})
                    if "content" in message_data:
                        content = message_data["content"]
                        if content:
                            if cur_msg_id is None:
                                cur_msg_id = str(uuid.uuid4())
                                yield AGUIEvent(
                                    "text_message.start",
                                    message_id=cur_msg_id,
                                    role="assistant",
                                ).sse()
                            yield AGUIEvent(
                                "text_message.content",
                                message_id=cur_msg_id,
                                delta=content,
                            ).sse()

                # ─── Emit new structured message types ──────────────────────────────────

                # Emit AgentStepMessage after each agent completes
                if typ == "end_of_agent":
                    agent_name = data.get("agent_name")
                    if agent_name:
                        agent_step_msg = AgentStepMessage(
                            agent_name=agent_name,
                            step_description=f"Completed {agent_name} agent processing",
                            step_status="completed",
                            results={"agent_id": data.get("agent_id")},
                        )
                        yield AGUIEvent(
                            "agent_step_message",
                            message_data=agent_step_msg.model_dump(),
                        ).sse()

                # Emit ToolOutputMessage after tool execution
                elif typ == "tool_call_result":
                    tool_name = data.get("tool_name")
                    tool_call_id = data.get("tool_call_id")
                    tool_result = data.get("tool_result", "")
                    if tool_name:
                        tool_output_msg = ToolOutputMessage(
                            tool_name=tool_name,
                            tool_call_id=tool_call_id,
                            tool_output=tool_result,
                            execution_status="success",
                        )
                        yield AGUIEvent(
                            "tool_output_message",
                            message_data=tool_output_msg.model_dump(),
                        ).sse()

                        # Emit ToDoListUpdateMessage for todo-md-mcp tool operations
                        if "todo" in tool_name.lower():
                            action_type = "update"
                            todo_id, todo_title, todo_status = None, None, None
                            metadata = {
                                "tool_name": tool_name,
                                "tool_result": tool_result,
                            }

                            if tool_call_id in tool_metadata:
                                tool_input = tool_metadata.pop(tool_call_id, {})
                                todo_title = tool_input.get("text")
                                if "metadata" in tool_input:
                                    metadata.update(tool_input["metadata"])

                            if "add_todo" in tool_name:
                                action_type = "add"
                            elif "update_todo" in tool_name:
                                action_type = "update"
                            elif "delete_todo" in tool_name:
                                action_type = "delete"
                            elif "complete" in tool_name.lower():
                                action_type = "complete"

                            try:
                                if isinstance(tool_result, str) and tool_result:
                                    import re

                                    id_match = re.search(
                                        r'"id":\s*"([a-f0-9\-]+)"', tool_result
                                    )
                                    if id_match:
                                        todo_id = id_match.group(1)

                                    if "completed" in tool_result.lower():
                                        todo_status = "completed"
                                    elif "in_progress" in tool_result.lower():
                                        todo_status = "in_progress"
                                    elif "pending" in tool_result.lower():
                                        todo_status = "pending"
                            except Exception:
                                pass

                            todo_update_msg = ToDoListUpdateMessage(
                                action_type=action_type,
                                todo_id=todo_id,
                                todo_title=todo_title,
                                todo_status=todo_status,
                                list_name="main",
                                metadata=metadata,
                            )
                            yield AGUIEvent(
                                "todo_list_update_message",
                                message_data=todo_update_msg.model_dump(),
                            ).sse()

        if cur_msg_id:
            yield AGUIEvent("text_message.end", message_id=cur_msg_id).sse()

        # ─── Emit FinalAnswerMessage when workflow completes ──────────────────────
        try:
            # Get final state to extract the final answer
            from src.service.workflow_service import get_graph

            current_graph = get_graph()
            if current_graph:
                config = {"configurable": {"thread_id": thread_id}}
                current_state = await current_graph.aget_state(config)
                if current_state and current_state.values.get("messages"):
                    # Get the last assistant message as the final answer
                    messages = current_state.values["messages"]
                    final_content = ""
                    for msg in reversed(messages):
                        if (
                            hasattr(msg, "content")
                            and msg.content
                            and hasattr(msg, "type")
                            and msg.type == "ai"
                        ):
                            final_content = msg.content
                            break
                        elif (
                            isinstance(msg, dict)
                            and msg.get("role") == "assistant"
                            and msg.get("content")
                        ):
                            final_content = msg["content"]
                            break

                    if final_content:
                        final_answer_msg = FinalAnswerMessage(
                            content=final_content,
                            agent_name="multi-agent-workflow",
                            analysis_summary="Multi-agent workflow analysis completed",
                        )
                        yield AGUIEvent(
                            "final_answer_message",
                            message_data=final_answer_msg.model_dump(),
                        ).sse()
        except Exception as e:
            log.warning(f"Could not extract final answer for FinalAnswerMessage: {e}")

        # run.finished
        yield AGUIEvent(
            "run.finished",
            run_id=run_id,
            thread_id=thread_id,
            finished_at=datetime.now(UTC).isoformat(),
        ).sse()

    except asyncio.CancelledError:
        yield AGUIEvent(
            "run.cancelled",
            run_id=run_id,
            thread_id=thread_id,
            reason="client",
        ).sse()
        raise
    except Exception as exc:  # noqa: BLE001
        log.exception("workflow error")
        yield AGUIEvent(
            "error",
            error_type="exec_error",
            message=str(exc),
        ).sse()
        yield AGUIEvent(
            "run.finished",
            run_id=run_id,
            thread_id=thread_id,
            error=str(exc),
            finished_at=datetime.now(UTC).isoformat(),
        ).sse()


@app.post("/awp")
async def awp(req: Request):
    data = RunInput(**await req.json())
    return StreamingResponse(
        _stream(
            data.thread_id,
            data.messages,
            data.run_id,
            data.state or {},
        ),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        },
    )


# ─── health probe -------------------------------------------------------
@app.get("/health")
def health():
    return {"ok": True, "ts": datetime.now(UTC).isoformat()}


@app.get("/conversations")
async def get_conversations():
    """Returns a list of all conversation threads."""
    try:
        threads = await list_conversation_threads()
        return {"threads": threads}
    except Exception as e:
        log.exception("Error listing conversations")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/conversations/{thread_id}/checkpoints")
async def get_checkpoints(thread_id: str):
    """Returns a list of checkpoints for a given thread."""
    try:
        checkpoints = await list_checkpoints_for_thread(thread_id)
        return {"checkpoints": checkpoints}
    except Exception as e:
        log.exception(f"Error listing checkpoints for thread {thread_id}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/conversations/{thread_id}/history")
async def get_history(thread_id: str):
    """Returns the conversation history for a given thread."""
    try:
        history = await get_conversation_history(thread_id)
        return {"history": history}
    except Exception as e:
        log.exception(f"Error getting history for thread {thread_id}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/conversations/{thread_id}/resume")
async def resume_conversation(thread_id: str, request: Request):
    """Resumes a conversation from a given checkpoint."""
    try:
        data = await request.json()
        checkpoint_ts = data.get("checkpoint_ts")
        if not checkpoint_ts:
            raise HTTPException(status_code=400, detail="checkpoint_ts is required")

        # The resume_from_checkpoint function in persistence_service.py returns a graph.
        # We need to decide how to handle this. For now, let's just return a success message.
        await resume_from_checkpoint(thread_id, checkpoint_ts)
        return {
            "message": (
                f"Conversation {thread_id} resumed from checkpoint {checkpoint_ts}"
            )
        }
    except Exception as e:
        log.exception(f"Error resuming conversation for thread {thread_id}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/conversations/{thread_id}/confirm")
async def handle_confirmation(thread_id: str, request: Request):
    """Handle user confirmation response and continue workflow."""
    try:
        data = await request.json()
        user_response = data.get("user_response")
        if not user_response:
            raise HTTPException(status_code=400, detail="user_response is required")

        # Return streaming response for continuation
        async def confirmation_stream():
            run_id = str(uuid.uuid4())
            yield AGUIEvent(
                "run.started",
                run_id=run_id,
                thread_id=thread_id,
                started_at=datetime.now(UTC).isoformat(),
            ).sse()

            cur_msg_id: Optional[str] = None
            try:
                async for event in handle_user_confirmation(thread_id, user_response):
                    if event:
                        typ, data = event.get("event", ""), event.get("data", {})
                        if typ == "on_chat_model_stream":
                            delta = data.get("chunk", {}).get("content", "")
                            if delta:
                                if cur_msg_id is None:
                                    cur_msg_id = str(uuid.uuid4())
                                    yield AGUIEvent(
                                        "text_message.start",
                                        message_id=cur_msg_id,
                                        role="assistant",
                                    ).sse()
                                yield AGUIEvent(
                                    "text_message.content",
                                    message_id=cur_msg_id,
                                    delta=delta,
                                ).sse()
                        elif typ == "on_tool_start":
                            yield AGUIEvent(
                                "tool_call.start",
                                tool_call_id=data.get("run_id"),
                                tool_call_name=data.get("name"),
                                parent_message_id=cur_msg_id,
                            ).sse()
                            yield AGUIEvent(
                                "tool_call.args",
                                tool_call_id=data.get("run_id"),
                                delta=json.dumps(data.get("input", {})),
                            ).sse()
                        elif typ == "on_tool_end":
                            yield AGUIEvent(
                                "tool_call.end",
                                tool_call_id=data.get("run_id"),
                            ).sse()
                            yield AGUIEvent(
                                "tool_call.result",
                                message_id=str(uuid.uuid4()),
                                tool_call_id=data.get("run_id"),
                                content=json.dumps(data.get("output", {})),
                                role="tool",
                            ).sse()
                        elif typ == "end_of_agent" and cur_msg_id:
                            yield AGUIEvent(
                                "text_message.end",
                                message_id=cur_msg_id,
                            ).sse()
                            cur_msg_id = None

                if cur_msg_id:
                    yield AGUIEvent("text_message.end", message_id=cur_msg_id).sse()

                yield AGUIEvent(
                    "run.finished",
                    run_id=run_id,
                    thread_id=thread_id,
                    finished_at=datetime.now(UTC).isoformat(),
                ).sse()

            except Exception as exc:
                log.exception("Confirmation handling error")
                yield AGUIEvent(
                    "error",
                    error_type="confirmation_error",
                    message=str(exc),
                ).sse()
                yield AGUIEvent(
                    "run.finished",
                    run_id=run_id,
                    thread_id=thread_id,
                    error=str(exc),
                    finished_at=datetime.now(UTC).isoformat(),
                ).sse()

        return StreamingResponse(
            confirmation_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",
            },
        )

    except Exception as e:
        log.exception(f"Error handling confirmation for thread {thread_id}")
        raise HTTPException(status_code=500, detail=str(e))
