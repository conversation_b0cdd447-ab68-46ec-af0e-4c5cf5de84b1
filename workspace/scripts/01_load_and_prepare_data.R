# Title: Load and Prepare Data for Differential Expression Analysis
# Description: Loads the expression matrix and prepares it for limma analysis.
# Created: 2025-08-02

# Error handling setup
options(error = function() {
  cat("ERROR: ", geterrmessage(), "\n")
  traceback()
  quit(status = 1)
})

# Load required libraries with error checking
required_packages <- c("limma", "data.table")
for(pkg in required_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("Installing package:", pkg, "\n")
    if(!require("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager")
    }
    BiocManager::install(pkg)
    library(pkg, character.only = TRUE)
  }
}

cat("Starting data loading and preparation...\n")

# Define file path
data_file <- "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/44620_GSE33447_expmat.data.txt"

# Load the dataset
# Using fread for faster loading of large datasets
expression_data <- fread(data_file, header = TRUE, sep = "\t", data.table = FALSE)

# Inspect the data
cat("Dimension of the loaded data:\n")
print(dim(expression_data))

cat("First few rows of the data:\n")
print(head(expression_data[, 1:5])) # Print first 5 columns to avoid overwhelming output

cat("Column names of the data:\n")
print(colnames(expression_data))

# Assuming the first column is probe IDs/gene names
rownames(expression_data) <- expression_data[, 1]
expression_data <- expression_data[, -1] # Remove the ID column

# Infer sample groups from column names
# This is a placeholder. Actual inference might require more sophisticated parsing
# or external metadata. For now, I'll assume a simple pattern.
# Let's assume columns ending with "T" are Tumor and "N" are Normal.
# This needs to be verified from the actual column names.
sample_names <- colnames(expression_data)
tumor_samples <- grep("GSM", sample_names, value = TRUE) # Placeholder, need to refine based on actual names
normal_samples <- grep("GSM", sample_names, value = TRUE) # Placeholder, need to refine based on actual names

# For now, let's assume the first half are normal and second half are tumor for demonstration
# This is a critical assumption and needs to be corrected based on actual sample annotation.
num_samples <- ncol(expression_data)
half_samples <- num_samples / 2

# This is a temporary assignment for demonstration.
# In a real scenario, I would need to examine the column names or a separate
# sample annotation file to correctly assign groups.
# For now, let's assume the first 10 samples are normal and the rest are tumor.
# This is a very weak assumption and will be corrected after inspecting the column names.
# I will use a more robust method after seeing the actual column names.

# Placeholder for group assignment - will be refined after inspecting column names
# For now, let's create a dummy group vector
group <- factor(c(rep("Normal", 10), rep("Tumor", num_samples - 10))) # This is a dummy, needs correction

# Create design matrix
# design <- model.matrix(~0 + group)
# colnames(design) <- levels(group)

# Save processed data and design matrix (optional, for next steps)
# saveRDS(expression_data, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/processed_expression_data.rds")
# saveRDS(design, "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/design_matrix.rds")

cat("Data loading and initial preparation completed. Review column names to refine group assignment.\n")
