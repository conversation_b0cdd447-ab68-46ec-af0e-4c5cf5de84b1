
# Title: Differential Expression Analysis of Breast Cancer Dataset - Limma Analysis
# Description: Performs differential expression analysis using limma package.
# Created: 2025-08-02

# Error handling setup
options(error = function() {
  cat("ERROR: ", geterrmessage(), "\n")
  traceback()
  quit(status = 1)
})

# Load required libraries with error checking
required_packages <- c("limma", "Biobase")
for(pkg in required_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("Installing package:", pkg, "\n")
    if(!require("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager")
    }
    BiocManager::install(pkg)
    library(pkg, character.only = TRUE)
  }
}

# Define file paths
output_dir <- "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/"

cat("Starting limma differential expression analysis...\n")

# Load prepared data
load(file = paste0(output_dir, "prepared_data.RData"))

# Fit linear model to expression data
fit <- lmFit(expression_data, design)

# Create contrast matrix for differential expression
# Comparing Cancer vs Normal
contrast_matrix <- makeContrasts(Cancer_vs_Normal = Cancer - Normal, levels = design)

# Apply contrasts to the fitted model
fit2 <- contrasts.fit(fit, contrast_matrix)

# Empirical Bayes moderation of standard errors
fit2 <- eBayes(fit2)

# Get top differentially expressed genes
top_genes <- topTable(fit2, coef="Cancer_vs_Normal", number = Inf, adjust.method = "BH")

# Add GeneSymbol and GeneName from original data if available
# (Assuming 'data' object is still available or re-loaded)
# For now, we'll just save the top_genes table.
# In a real scenario, you might merge with 'data' based on 'Probe' column.

# Save results
write.table(top_genes, file = paste0(output_dir, "differentially_expressed_genes.txt"), sep = "\t", quote = FALSE, row.names = TRUE)

cat("Differential expression analysis completed successfully. Results saved to differentially_expressed_genes.txt\n")

# Print summary of differentially expressed genes (e.g., number of up/down regulated at a certain FDR)
summary_results <- decideTests(fit2)
cat("\nSummary of differentially expressed genes (FDR < 0.05):\n")
print(summary_results)
