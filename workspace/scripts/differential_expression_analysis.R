
# Title: Differential Expression Analysis of Breast Cancer Dataset
# Description: Performs differential expression analysis comparing breast cancer vs. normal breast tissues using limma.
# Created: 2025-08-02

# Error handling setup
options(error = function() {
  cat("ERROR: ", geterrmessage(), "\n")
  traceback()
  quit(status = 1)
})

# Load required libraries with error checking
required_packages <- c("limma", "Biobase")
for(pkg in required_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("Installing package:", pkg, "\n")
    if(!require("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager")
    }
    BiocManager::install(pkg)
    library(pkg, character.only = TRUE)
  }
}

cat("Starting differential expression analysis...\n")

# Define the input and output file paths
input_file <- "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/44620_GSE33447_expmat.data.txt"
output_file <- "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/differentially_expressed_genes.csv"

# Read the expression data
# Skip initial comment lines starting with '#'
data <- read.delim(input_file, comment.char = "#", header = TRUE, stringsAsFactors = FALSE)

# Extract expression matrix: columns from 'GSE33447_Biomat_14___BioAssayId.1088953Name.Breastnon.cancertissueReplicate8' onwards are expression values
# Identify expression columns dynamically
exp_cols_start_idx <- which(colnames(data) == "GSE33447_Biomat_14___BioAssayId.1088953Name.Breastnon.cancertissueReplicate8")
expression_matrix <- as.matrix(data[, exp_cols_start_idx:ncol(data)])
rownames(expression_matrix) <- data$Probe

# Create design matrix
# Sample names contain "Breastnon.cancertissue" for normal and "Breastcancertissue" for cancer
sample_names <- colnames(expression_matrix)
group <- factor(ifelse(grepl("Breastcancertissue", sample_names), "Cancer", "Normal"), levels = c("Normal", "Cancer"))
design <- model.matrix(~0 + group)
colnames(design) <- levels(group)

# Perform differential expression analysis using limma
fit <- lmFit(expression_matrix, design)

# Define contrasts: Cancer vs Normal
contrast_matrix <- makeContrasts(Cancer_vs_Normal = Cancer - Normal, levels = design)
fit2 <- contrasts.fit(fit, contrast_matrix)
fit2 <- eBayes(fit2)

# Get differentially expressed genes
# Adjust p-value using Benjamini-Hochberg (FDR)
# You can adjust the p-value cutoff (e.g., 0.05) and logFC cutoff (e.g., 1.0) as needed
results <- topTable(fit2, adjust.method = "BH", number = Inf)

# Filter for differentially expressed genes (e.g., adjusted p-value < 0.05 and |logFC| > 1)
# For now, I will output all results and let the user decide on the cutoffs.
# If specific cutoffs are provided, I will apply them in the next iteration.

# Add GeneSymbol and GeneName to the results
results$GeneSymbol <- data$GeneSymbol[match(rownames(results), data$Probe)]
results$GeneName <- data$GeneName[match(rownames(results), data$Probe)]

# Reorder columns for better readability
results <- results[, c("GeneSymbol", "GeneName", "logFC", "AveExpr", "t", "P.Value", "adj.P.Val", "B")]

# Save the results to a CSV file
write.csv(results, file = output_file, row.names = FALSE)

cat("Differential expression analysis completed. Results saved to:", output_file, "\n")
