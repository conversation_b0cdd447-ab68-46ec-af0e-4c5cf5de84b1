# Title: Differential Expression Analysis with limma
# Description: Performs differential expression analysis comparing breast cancer vs. normal breast tissues using limma.
# Created: 2025-08-02

# Error handling setup
options(error = function() {
  cat("ERROR: ", geterrmessage(), "\n")
  traceback()
  quit(status = 1)
})

# Load required libraries with error checking
required_packages <- c("limma", "data.table")
for(pkg in required_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("Installing package:", pkg, "\n")
    if(!require("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager")
    }
    BiocManager::install(pkg)
    library(pkg, character.only = TRUE)
  }
}

cat("Starting differential expression analysis...\n")

# Define file path
data_file <- "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/44620_GSE33447_expmat.data.txt"

# Load the dataset
expression_data <- fread(data_file, header = TRUE, sep = "\t", data.table = FALSE)

# Set Probe IDs as row names and remove the first few annotation columns
# Based on previous output, columns 1-6 are annotation columns.
# Columns 7-22 are expression values.
rownames(expression_data) <- expression_data[, 1] # Assuming Probe is the first column
expression_values <- expression_data[, 7:22] # Extract only expression value columns

# Ensure expression values are numeric
expression_values <- as.matrix(expression_values)

# Define sample groups based on column names
sample_names <- colnames(expression_values)

# Identify normal and tumor samples based on the observed column name patterns
normal_samples_indices <- grep("Breastnon.cancertissue", sample_names)
tumor_samples_indices <- grep("Breastcancertissue", sample_names)

# Create group factor
group <- factor(c(rep("Normal", length(normal_samples_indices)), 
                  rep("Tumor", length(tumor_samples_indices))), 
                levels = c("Normal", "Tumor"))

# Create design matrix
design <- model.matrix(~0 + group)
colnames(design) <- levels(group)

cat("Design matrix created:\n")
print(design)

# Fit linear model to expression data
fit <- lmFit(expression_values, design)

# Create contrast matrix for differential expression
contrast.matrix <- makeContrasts(TumorVsNormal = Tumor - Normal, levels = design)

cat("Contrast matrix created:\n")
print(contrast.matrix)

# Apply contrasts to the fitted model
fit2 <- contrasts.fit(fit, contrast.matrix)

# Empirical Bayes moderation of standard errors
fit2 <- eBayes(fit2)

# Get differentially expressed genes
toptable_results <- topTable(fit2, coef = "TumorVsNormal", number = Inf, adjust.method = "BH")

cat("Top differentially expressed genes (first 10 rows):\n")
print(head(toptable_results, 10))

# Save the results
output_file <- "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/differentially_expressed_genes.csv"
write.csv(toptable_results, file = output_file, row.names = TRUE)

cat("Differential expression analysis completed. Results saved to:", output_file, "\n")
