
# Title: Differential Expression Analysis of Breast Cancer Dataset
# Description: Performs differential expression analysis using limma
# Created: 2025-08-02

# Error handling setup
options(error = function() {
  cat("ERROR: ", geterrmessage(), "\n")
  traceback()
  quit(status = 1)
})

# Load required libraries with error checking
required_packages <- c("limma", "GEOquery")
for(pkg in required_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("Installing package:", pkg, "\n")
    if(!require("BiocManager", quietly = TRUE)) {
      install.packages("BiocManager")
    }
    BiocManager::install(pkg)
    library(pkg, character.only = TRUE)
  }
}

cat("Starting differential expression analysis...\n")

# Define file paths
data_file <- "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/44620_GSE33447_expmat.data.txt"
output_file <- "/Users/<USER>/Documents/Startup/codex/agentic_workflows_comparisons/multiagent_langgraph/workspace/outputs/differentially_expressed_genes.csv"

# Load the expression matrix
# Read all columns as is, then select numeric ones
expression_data_raw <- read.table(data_file, sep = "\t", header = TRUE, row.names = 1, check.names = FALSE, stringsAsFactors = FALSE)

# Identify and select only the numeric (expression) columns
# Assuming expression columns start from the 7th column based on the file preview
expression_data <- expression_data_raw[, 7:ncol(expression_data_raw)]

# Ensure all selected columns are numeric
expression_data <- as.matrix(expression_data)

# Debugging: Print dimensions of expression data
cat("Dimensions of expression data (expression_data): ", dim(expression_data)[1], " rows (genes), ", dim(expression_data)[2], " columns (samples)\n")

# Extract sample names from column names of the selected expression data
sample_names <- colnames(expression_data)

# Create design matrix
# Use grepl to identify 'cancer' and 'normal' samples from the sample names
group <- ifelse(grepl("Breastcancertissue", sample_names, ignore.case = TRUE), "Cancer", "Normal")

design <- model.matrix(~0 + factor(group))
colnames(design) <- levels(factor(group))

# Debugging: Print dimensions of design matrix
cat("Dimensions of design matrix (design): ", dim(design)[1], " rows, ", dim(design)[2], " columns\n")

# Perform differential expression analysis
fit <- lmFit(expression_data, design)

# Define contrasts (Cancer vs Normal)
contrast.matrix <- makeContrasts(Cancer_vs_Normal = Cancer - Normal, levels = design)
fit2 <- contrasts.fit(fit, contrast.matrix)
fit2 <- eBayes(fit2)

# Get differentially expressed genes
# Adjust p-value using Benjamini-Hochberg (BH) method
# Filter by adjusted p-value < 0.05 and logFC > 1 or logFC < -1
results <- topTable(fit2, coef = "Cancer_vs_Normal", number = Inf, adjust.method = "BH")
diff_genes <- subset(results, adj.P.Val < 0.05 & abs(logFC) > 1)

# Add GeneSymbol and GeneName back to the results for easier interpretation
# Match by row names (Probe IDs)
diff_genes$GeneSymbol <- expression_data_raw[rownames(diff_genes), "GeneSymbol"]
diff_genes$GeneName <- expression_data_raw[rownames(diff_genes), "GeneName"]

# Reorder columns to have GeneSymbol and GeneName first
diff_genes <- diff_genes[, c("GeneSymbol", "GeneName", setdiff(colnames(diff_genes), c("GeneSymbol", "GeneName")))]

# Save the results
write.csv(diff_genes, file = output_file, row.names = TRUE)

cat("Differential expression analysis completed. Results saved to:", output_file, "\n")
