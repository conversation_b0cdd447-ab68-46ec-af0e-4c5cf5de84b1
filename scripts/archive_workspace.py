#!/usr/bin/env python3
"""
Workspace Archive Manager for Multi-Agent LangGraph System

This script creates timestamped archives of workspace contents before new workflow runs,
preventing clutter and preserving previous analysis results for reference.

Key Features:
- Creates timestamped ZIP archives in `archives/` directory
- Preserves all analysis outputs, scripts, and metadata
- Configurable size and age limits for archive management
- Atomic operations to prevent data corruption
- Comprehensive logging for audit trails

Usage:
    python scripts/archive_workspace.py [--force] [--dry-run]
"""

import os
import sys
import shutil
import zipfile
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Tuple, Optional
import argparse
import json


class WorkspaceArchiver:
    """Manages workspace archiving with configurable retention policies."""
    
    def __init__(self, workspace_dir: str = "workspace", archive_dir: str = "archives"):
        self.workspace_path = Path(workspace_dir).resolve()
        self.archive_path = Path(archive_dir).resolve()
        self.logger = self._setup_logging()
        
        # Configuration
        self.max_archives = 10  # Keep last 10 archives
        self.max_archive_age_days = 30  # Keep archives for 30 days
        self.min_workspace_size_mb = 1  # Only archive if workspace > 1MB
        
        # Files/dirs to exclude from archiving
        self.exclude_patterns = {
            '.DS_Store',
            '__pycache__',
            '*.pyc',
            '.git',
            '.gitignore',
            'node_modules',
            '.env'
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Configure logging for archive operations."""
        logger = logging.getLogger('workspace_archiver')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _should_exclude(self, file_path: Path) -> bool:
        """Check if file should be excluded from archive."""
        name = file_path.name
        for pattern in self.exclude_patterns:
            if pattern.startswith('*'):
                if name.endswith(pattern[1:]):
                    return True
            elif name == pattern:
                return True
        return False
    
    def _get_workspace_size(self) -> float:
        """Calculate workspace size in MB."""
        total_size = 0
        try:
            for root, dirs, files in os.walk(self.workspace_path):
                for file in files:
                    file_path = Path(root) / file
                    if not self._should_exclude(file_path):
                        try:
                            total_size += file_path.stat().st_size
                        except (OSError, FileNotFoundError):
                            self.logger.warning(f"Could not get size for {file_path}")
        except Exception as e:
            self.logger.error(f"Error calculating workspace size: {e}")
            return 0
        
        return total_size / (1024 * 1024)  # Convert to MB
    
    def _create_archive_metadata(self, archive_path: Path, workspace_size: float) -> dict:
        """Create metadata for the archive."""
        metadata = {
            'created_at': datetime.now().isoformat(),
            'workspace_size_mb': round(workspace_size, 2),
            'archive_path': str(archive_path),
            'file_count': 0,
            'directories': [],
            'analysis_files': []
        }
        
        # Count files and identify analysis artifacts
        try:
            for root, dirs, files in os.walk(self.workspace_path):
                root_path = Path(root)
                rel_root = root_path.relative_to(self.workspace_path)
                
                if rel_root != Path('.'):
                    metadata['directories'].append(str(rel_root))
                
                for file in files:
                    file_path = root_path / file
                    if not self._should_exclude(file_path):
                        metadata['file_count'] += 1
                        
                        # Identify important analysis files
                        if file.endswith(('.md', '.csv', '.json', '.R', '.py', '.txt')):
                            rel_path = file_path.relative_to(self.workspace_path)
                            metadata['analysis_files'].append(str(rel_path))
        
        except Exception as e:
            self.logger.error(f"Error creating metadata: {e}")
        
        return metadata
    
    def _save_metadata(self, metadata: dict, archive_path: Path):
        """Save archive metadata as JSON."""
        metadata_path = archive_path.with_suffix('.json')
        try:
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            self.logger.info(f"Metadata saved to {metadata_path}")
        except Exception as e:
            self.logger.error(f"Failed to save metadata: {e}")
    
    def create_archive(self, force: bool = False, dry_run: bool = False) -> Optional[Path]:
        """
        Create a timestamped archive of the workspace.
        
        Args:
            force: Skip size checks and force archival
            dry_run: Simulate the operation without creating files
            
        Returns:
            Path to created archive or None if skipped
        """
        if not self.workspace_path.exists():
            self.logger.info(f"Workspace directory {self.workspace_path} does not exist")
            return None
        
        # Check workspace size
        workspace_size = self._get_workspace_size()
        if not force and workspace_size < self.min_workspace_size_mb:
            self.logger.info(
                f"Workspace size ({workspace_size:.2f}MB) below threshold "
                f"({self.min_workspace_size_mb}MB), skipping archive"
            )
            return None
        
        # Create archive directory
        if not dry_run:
            self.archive_path.mkdir(exist_ok=True)
        
        # Generate archive filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        archive_name = f"workspace_archive_{timestamp}.zip"
        archive_file_path = self.archive_path / archive_name
        
        if dry_run:
            self.logger.info(f"DRY RUN: Would create archive {archive_file_path}")
            self.logger.info(f"DRY RUN: Workspace size: {workspace_size:.2f}MB")
            return archive_file_path
        
        try:
            self.logger.info(f"Creating archive: {archive_file_path}")
            self.logger.info(f"Workspace size: {workspace_size:.2f}MB")
            
            # Create the ZIP archive
            with zipfile.ZipFile(archive_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(self.workspace_path):
                    for file in files:
                        file_path = Path(root) / file
                        if not self._should_exclude(file_path):
                            try:
                                # Calculate relative path for archive
                                rel_path = file_path.relative_to(self.workspace_path)
                                zipf.write(file_path, rel_path)
                            except Exception as e:
                                self.logger.warning(f"Failed to add {file_path} to archive: {e}")
            
            # Create and save metadata
            metadata = self._create_archive_metadata(archive_file_path, workspace_size)
            self._save_metadata(metadata, archive_file_path)
            
            self.logger.info(f"Archive created successfully: {archive_file_path}")
            self.logger.info(f"Archived {metadata['file_count']} files")
            
            return archive_file_path
            
        except Exception as e:
            self.logger.error(f"Failed to create archive: {e}")
            # Cleanup partial archive
            if archive_file_path.exists():
                try:
                    archive_file_path.unlink()
                except Exception:
                    pass
            return None
    
    def clean_workspace(self, preserve_patterns: List[str] = None, dry_run: bool = False):
        """
        Clean the workspace after archiving, preserving specified patterns.
        
        Args:
            preserve_patterns: List of glob patterns to preserve (e.g., ['*.env', 'config/*'])
            dry_run: Simulate the operation without deleting files
        """
        if not self.workspace_path.exists():
            return
        
        preserve_patterns = preserve_patterns or []
        preserved_files = set()
        
        # Find files to preserve
        for pattern in preserve_patterns:
            for file_path in self.workspace_path.glob(pattern):
                preserved_files.add(file_path.resolve())
        
        if dry_run:
            self.logger.info("DRY RUN: Would clean workspace, preserving:")
            for file_path in preserved_files:
                rel_path = file_path.relative_to(self.workspace_path)
                self.logger.info(f"  - {rel_path}")
            return
        
        try:
            self.logger.info("Cleaning workspace...")
            
            # Remove all files and directories except preserved ones
            for item in self.workspace_path.iterdir():
                if item.resolve() not in preserved_files:
                    try:
                        if item.is_file():
                            item.unlink()
                            self.logger.debug(f"Removed file: {item}")
                        elif item.is_dir():
                            shutil.rmtree(item)
                            self.logger.debug(f"Removed directory: {item}")
                    except Exception as e:
                        self.logger.warning(f"Failed to remove {item}: {e}")
            
            self.logger.info("Workspace cleaned successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to clean workspace: {e}")
    
    def cleanup_old_archives(self, dry_run: bool = False):
        """Remove old archives based on retention policies."""
        if not self.archive_path.exists():
            return
        
        try:
            # Get all archive files
            archive_files = list(self.archive_path.glob("workspace_archive_*.zip"))
            archive_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            cutoff_date = datetime.now() - timedelta(days=self.max_archive_age_days)
            files_to_remove = []
            
            # Mark files for removal by age
            for archive_file in archive_files:
                mod_time = datetime.fromtimestamp(archive_file.stat().st_mtime)
                if mod_time < cutoff_date:
                    files_to_remove.append(archive_file)
            
            # Mark files for removal by count (keep only max_archives)
            if len(archive_files) > self.max_archives:
                files_to_remove.extend(archive_files[self.max_archives:])
            
            # Remove duplicates
            files_to_remove = list(set(files_to_remove))
            
            if not files_to_remove:
                self.logger.info("No old archives to cleanup")
                return
            
            if dry_run:
                self.logger.info("DRY RUN: Would remove old archives:")
                for file_path in files_to_remove:
                    self.logger.info(f"  - {file_path.name}")
                return
            
            # Remove old archives and their metadata
            for archive_file in files_to_remove:
                try:
                    archive_file.unlink()
                    self.logger.info(f"Removed old archive: {archive_file.name}")
                    
                    # Remove corresponding metadata file
                    metadata_file = archive_file.with_suffix('.json')
                    if metadata_file.exists():
                        metadata_file.unlink()
                        self.logger.debug(f"Removed metadata: {metadata_file.name}")
                        
                except Exception as e:
                    self.logger.warning(f"Failed to remove {archive_file}: {e}")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old archives: {e}")
    
    def list_archives(self) -> List[Tuple[Path, dict]]:
        """List all archives with their metadata."""
        archives = []
        
        if not self.archive_path.exists():
            return archives
        
        try:
            for archive_file in self.archive_path.glob("workspace_archive_*.zip"):
                metadata_file = archive_file.with_suffix('.json')
                metadata = {}
                
                if metadata_file.exists():
                    try:
                        with open(metadata_file, 'r') as f:
                            metadata = json.load(f)
                    except Exception as e:
                        self.logger.warning(f"Failed to read metadata for {archive_file}: {e}")
                
                archives.append((archive_file, metadata))
            
            # Sort by creation time (newest first)
            archives.sort(key=lambda x: x[1].get('created_at', ''), reverse=True)
            
        except Exception as e:
            self.logger.error(f"Failed to list archives: {e}")
        
        return archives


def main():
    """Main entry point for the archive script."""
    parser = argparse.ArgumentParser(
        description="Archive workspace contents before new workflow runs"
    )
    parser.add_argument(
        '--force', 
        action='store_true',
        help='Force archival regardless of workspace size'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true', 
        help='Simulate the operation without creating/deleting files'
    )
    parser.add_argument(
        '--clean-only',
        action='store_true',
        help='Only clean old archives, do not create new archive'
    )
    parser.add_argument(
        '--list',
        action='store_true',
        help='List existing archives and exit'
    )
    parser.add_argument(
        '--preserve',
        nargs='*',
        default=[],
        help='Patterns of files to preserve during workspace cleanup'
    )
    
    args = parser.parse_args()
    
    # Initialize archiver
    archiver = WorkspaceArchiver()
    
    if args.list:
        archives = archiver.list_archives()
        if archives:
            print(f"\nFound {len(archives)} archives:")
            for archive_file, metadata in archives:
                created_at = metadata.get('created_at', 'Unknown')
                size_mb = metadata.get('workspace_size_mb', 0)
                file_count = metadata.get('file_count', 0)
                print(f"  {archive_file.name}")
                print(f"    Created: {created_at}")
                print(f"    Size: {size_mb}MB ({file_count} files)")
        else:
            print("No archives found")
        return
    
    try:
        if not args.clean_only:
            # Create new archive
            archive_path = archiver.create_archive(
                force=args.force, 
                dry_run=args.dry_run
            )
            
            if archive_path and not args.dry_run:
                # Clean workspace after successful archival
                archiver.clean_workspace(
                    preserve_patterns=args.preserve,
                    dry_run=args.dry_run
                )
        
        # Cleanup old archives
        archiver.cleanup_old_archives(dry_run=args.dry_run)
        
    except KeyboardInterrupt:
        archiver.logger.info("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        archiver.logger.error(f"Archive operation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()