#!/usr/bin/env python3
"""
Modern CLI for Omiy Multi-Agent Bioinformatics Workflow System

Built with Typer for a responsive, command-based user experience with real-time streaming.
"""

import asyncio
import json
import os
import sys
import uuid
from pathlib import Path
from typing import Optional, List

import requests
import typer
from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich.text import Text
from langchain_core.messages import HumanMessage

# Import the workflow service for direct execution
from src.service.workflow_service import run_agent_workflow

# --- Configuration ---
BASE_URL = os.environ.get("AWC_BASE_URL", "http://localhost:8000")
TODO_FILE_PATH = "todo.md"
console = Console()
app = typer.Typer(
    name="omiy",
    help="Omiy Multi-Agent AI Bioinformatics Workflow System",
    no_args_is_help=True,
    rich_markup_mode="rich",
)


class StreamingState:
    """Manages streaming output state for real-time display."""
    
    def __init__(self):
        self.current_message = ""
        self.current_agent = "Assistant"
        self.message_buffer = []
        
    def add_content(self, content: str, agent: str = None):
        """Add content to the current message buffer."""
        if agent:
            self.current_agent = agent
        self.current_message += content
        
    def flush_message(self):
        """Flush the current message and display it."""
        if self.current_message.strip():
            self._display_agent_message(self.current_message, self.current_agent)
            self.current_message = ""
            
    def _display_agent_message(self, content: str, agent: str):
        """Display an agent message with proper formatting."""
        agent_name = agent.title() if agent else "Assistant"
        console.print(
            Panel(
                content,
                title=f"[bold green]🤖 {agent_name}[/bold green]",
                border_style="green",
                padding=(1, 2),
            )
        )


def reset_todo_file():
    """Reset the todo.md file for a new session."""
    try:
        with open(TODO_FILE_PATH, "w") as f:
            f.write("# Todo List\n\n<!-- Generated by Omiy Multi-Agent System -->\n")
    except Exception as e:
        console.print(f"[yellow]Warning: Could not reset todo file: {e}[/yellow]")


def read_file_content(file_path: Path) -> str:
    """Read content from a file with error handling."""
    try:
        return file_path.read_text(encoding="utf-8")
    except Exception as e:
        console.print(f"[red]Error reading file {file_path}: {e}[/red]")
        raise typer.Exit(1)


def validate_file_exists(file_path: str) -> Path:
    """Validate that a file exists and return Path object."""
    path = Path(file_path)
    if not path.exists():
        console.print(f"[red]Error: File '{file_path}' does not exist[/red]")
        raise typer.Exit(1)
    if not path.is_file():
        console.print(f"[red]Error: '{file_path}' is not a file[/red]")
        raise typer.Exit(1)
    return path


async def stream_workflow_execution(
    user_input: str,
    thread_id: str = None,
    debug: bool = False,
    deep_thinking: bool = False,
    search_before_planning: bool = False,
):
    """Execute workflow with real-time streaming output."""
    if thread_id is None:
        thread_id = str(uuid.uuid4())
    
    console.print(f"[dim]🧵 Thread ID: {thread_id}[/dim]")
    console.print(f"[dim]📝 Query: {user_input}[/dim]")
    console.print("[dim]🚀 Starting workflow...[/dim]\n")
    
    state = StreamingState()
    
    try:
        # Create message list in the format expected by the workflow service
        messages = [HumanMessage(content=user_input, name="user")]
        
        # Stream events from the workflow
        async for event in run_agent_workflow(
            user_input_messages=messages,
            thread_id=thread_id,
            debug=debug,
            deep_thinking_mode=deep_thinking,
            search_before_planning=search_before_planning,
        ):
            event_type = event.get("event")
            data = event.get("data", {})
            
            if debug:
                console.print(f"[dim yellow]🔍 DEBUG: {event_type} - {event}[/dim yellow]")
            
            # Handle different event types for real-time display
            if event_type == "start_of_agent":
                agent_name = data.get("agent_name", "Unknown")
                console.print(f"[cyan]▶️  Starting {agent_name.title()} agent...[/cyan]")
                
            elif event_type == "message":
                delta = data.get("delta", {})
                if "content" in delta:
                    # Real-time token streaming - print immediately
                    content = delta["content"]
                    if content:
                        console.print(content, end="", style="green")
                        sys.stdout.flush()
                elif "reasoning_content" in delta:
                    # Show reasoning content if available
                    reasoning = delta["reasoning_content"]
                    if reasoning and debug:
                        console.print(f"[dim blue]💭 Reasoning: {reasoning}[/dim blue]")
                        
            elif event_type == "end_of_agent":
                agent_name = data.get("agent_name", "Unknown")
                console.print(f"\n[cyan]✅ {agent_name.title()} agent completed[/cyan]\n")
                
            elif event_type == "tool_call":
                tool_name = data.get("tool_name", "Unknown")
                console.print(f"[yellow]🔧 Using tool: {tool_name}[/yellow]")
                
            elif event_type == "tool_call_result":
                tool_name = data.get("tool_name", "Unknown")
                result_preview = str(data.get("tool_result", ""))[:100]
                console.print(f"[dim yellow]✓ {tool_name} result: {result_preview}...[/dim yellow]")
                
            elif event_type == "end_of_workflow":
                console.print("\n[bold green]✅ Workflow completed successfully![/bold green]")
                break
                
    except Exception as e:
        console.print(f"[red]❌ Error during workflow execution: {e}[/red]")
        if debug:
            import traceback
            console.print(f"[red]{traceback.format_exc()}[/red]")
        raise typer.Exit(1)


@app.command("run-bio-analysis")
def run_bio_analysis(
    input_file: str = typer.Option(
        ...,
        "--input", 
        "-i",
        help="Path to input file containing the bioinformatics analysis request"
    ),
    thread_id: Optional[str] = typer.Option(
        None,
        "--thread-id",
        "-t", 
        help="Optional thread ID to continue existing conversation"
    ),
    debug: bool = typer.Option(
        False,
        "--debug",
        "-d",
        help="Enable debug mode for verbose logging"
    ),
    deep_thinking: bool = typer.Option(
        False,
        "--deep-thinking",
        help="Enable deep thinking mode for complex analysis"
    ),
    search_first: bool = typer.Option(
        False,
        "--search-first",
        help="Search for relevant information before planning"
    ),
):
    """
    Run a bioinformatics analysis workflow from an input file.
    
    The input file should contain a description of the analysis you want to perform,
    including any specific requirements, data sources, or methodologies.
    
    Example:
        omiy run-bio-analysis --input analysis_request.txt
        omiy run-bio-analysis -i request.md --debug --deep-thinking
    """
    console.print("[bold blue]🔬 Omiy Bioinformatics Analysis Workflow[/bold blue]\n")
    
    # Validate and read input file
    input_path = validate_file_exists(input_file)
    user_input = read_file_content(input_path)
    
    if not user_input.strip():
        console.print(f"[red]Error: Input file '{input_file}' is empty[/red]")
        raise typer.Exit(1)
    
    console.print(f"[cyan]📄 Input file: {input_file}[/cyan]")
    console.print(f"[cyan]🔍 Deep thinking: {'Enabled' if deep_thinking else 'Disabled'}[/cyan]")
    console.print(f"[cyan]🔎 Search first: {'Enabled' if search_first else 'Disabled'}[/cyan]")
    console.print("" + "="*60 + "\n")
    
    # Reset todo file for new analysis
    reset_todo_file()
    
    # Run the workflow
    asyncio.run(
        stream_workflow_execution(
            user_input=user_input,
            thread_id=thread_id,
            debug=debug,
            deep_thinking=deep_thinking,
            search_before_planning=search_first,
        )
    )


@app.command("new-app")
def new_app(
    idea_file: str = typer.Option(
        ...,
        "--idea",
        "-i",
        help="Path to file containing the application idea or requirements"
    ),
    thread_id: Optional[str] = typer.Option(
        None,
        "--thread-id",
        "-t",
        help="Optional thread ID to continue existing conversation"
    ),
    debug: bool = typer.Option(
        False,
        "--debug",
        "-d",
        help="Enable debug mode for verbose logging"
    ),
    deep_thinking: bool = typer.Option(
        False,
        "--deep-thinking",
        help="Enable deep thinking mode for complex application development"
    ),
):
    """
    Create a new application from an idea file.
    
    The idea file should contain a description of the application you want to build,
    including features, requirements, technology preferences, and any specific constraints.
    
    Example:
        omiy new-app --idea app_idea.txt
        omiy new-app -i idea.md --debug --deep-thinking
    """
    console.print("[bold blue]🚀 Omiy Application Development Workflow[/bold blue]\n")
    
    # Validate and read idea file
    idea_path = validate_file_exists(idea_file)
    idea_content = read_file_content(idea_path)
    
    if not idea_content.strip():
        console.print(f"[red]Error: Idea file '{idea_file}' is empty[/red]")
        raise typer.Exit(1)
    
    # Prepend context to help the AI understand this is an app development request
    user_input = f"""I want to develop a new application based on the following idea and requirements:

{idea_content}

Please help me design, plan, and implement this application. Consider the architecture, technology stack, features, and provide code examples where appropriate."""
    
    console.print(f"[cyan]💡 Idea file: {idea_file}[/cyan]")
    console.print(f"[cyan]🔍 Deep thinking: {'Enabled' if deep_thinking else 'Disabled'}[/cyan]")
    console.print("" + "="*60 + "\n")
    
    # Reset todo file for new project
    reset_todo_file()
    
    # Run the workflow
    asyncio.run(
        stream_workflow_execution(
            user_input=user_input,
            thread_id=thread_id,
            debug=debug,
            deep_thinking=deep_thinking,
            search_before_planning=True,  # Always search for app development
        )
    )


@app.command("interactive")
def interactive_mode(
    thread_id: Optional[str] = typer.Option(
        None,
        "--thread-id",
        "-t",
        help="Optional thread ID to continue existing conversation"
    ),
    debug: bool = typer.Option(
        False,
        "--debug",
        "-d",
        help="Enable debug mode for verbose logging"
    ),
):
    """
    Start an interactive chat session with the multi-agent system.
    
    This provides a conversational interface where you can ask questions,
    request analyses, or get help with various tasks.
    
    Example:
        omiy interactive
        omiy interactive --thread-id existing-thread-123 --debug
    """
    if thread_id is None:
        thread_id = str(uuid.uuid4())
        console.print(f"[dim]🧵 Starting new conversation with Thread ID: {thread_id}[/dim]")
        reset_todo_file()
    else:
        console.print(f"[dim]🧵 Continuing conversation with Thread ID: {thread_id}[/dim]")
    
    console.print("[bold blue]💬 Omiy Interactive Mode[/bold blue]")
    console.print("[dim]Type 'exit', 'quit', or press Ctrl+C to end the session.[/dim]\n")
    
    try:
        while True:
            try:
                user_input = typer.prompt("[bold blue]You[/bold blue]")
                
                if user_input.lower().strip() in ["exit", "quit", "bye"]:
                    console.print("[dim]👋 Goodbye![/dim]")
                    break
                
                console.print("")
                asyncio.run(
                    stream_workflow_execution(
                        user_input=user_input,
                        thread_id=thread_id,
                        debug=debug,
                    )
                )
                console.print("\n" + "="*60 + "\n")
                
            except KeyboardInterrupt:
                console.print("\n[dim]👋 Session interrupted. Goodbye![/dim]")
                break
            except Exception as e:
                console.print(f"[red]❌ Error: {e}[/red]")
                continue
                
    except KeyboardInterrupt:
        console.print("\n[dim]👋 Goodbye![/dim]")


@app.command("status")
def status():
    """
    Show system status and configuration information.
    """
    console.print("[bold blue]📊 Omiy System Status[/bold blue]\n")
    
    # Create status table
    table = Table(show_header=True, header_style="bold blue")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="dim")
    
    # Check API server
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        api_status = "✅ Online" if response.status_code == 200 else "❌ Error"
        api_details = f"Status: {response.status_code}"
    except Exception as e:
        api_status = "❌ Offline"
        api_details = str(e)
    
    table.add_row("API Server", api_status, api_details)
    table.add_row("Base URL", "ℹ️  Configured", BASE_URL)
    
    # Check todo file
    todo_status = "✅ Available" if os.path.exists(TODO_FILE_PATH) else "❌ Missing"
    table.add_row("Todo File", todo_status, TODO_FILE_PATH)
    
    console.print(table)
    console.print("\n[dim]Use 'omiy --help' to see available commands.[/dim]")


@app.callback()
def main(
    version: bool = typer.Option(
        False,
        "--version",
        "-v",
        help="Show version information"
    )
):
    """
    Omiy Multi-Agent AI Bioinformatics Workflow System
    
    A modern CLI for running sophisticated AI workflows for bioinformatics analysis,
    application development, and interactive problem-solving.
    """
    if version:
        console.print("[bold blue]Omiy Multi-Agent System[/bold blue]")
        console.print("Version: 0.1.0")
        console.print("Built with ❤️  using LangGraph and Typer")
        raise typer.Exit()


if __name__ == "__main__":
    app()
