[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "omiy"
version = "0.1.0"
description = "Omiy - Multi-Agent AI Automation Framework"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "httpx>=0.28.1",
    "langchain-community>=0.3.19",
    "langchain-experimental>=0.3.4",
    "langchain-openai>=0.3.8",
    "langgraph>=0.3.5",
    "langgraph-checkpoint-sqlite>=2.0.10",
    "aiosqlite>=0.21.0",
    "readabilipy>=0.3.0",
    "python-dotenv>=1.0.1",
    "socksio>=1.0.0",
    "markdownify>=1.1.0",
    "browser-use>=0.1.0",
    "fastapi>=0.110.0",
    "uvicorn>=0.27.1",
    "sse-starlette>=1.6.5",
    "pandas>=2.2.3",
    "numpy>=2.2.3",
    "yfinance>=0.2.54",
    "langchain-google-genai>=0.3.0",
    "langchain-deepseek>=0.1.2",
    "pydantic>=2.0.0",
    "python-multipart>=0.0.6",
    "langchain_mcp_adapters>=0.1.7",
    "nest-asyncio>=1.6.0",
    "typer>=0.9.0",
    "rich>=13.0.0"
]

[project.optional-dependencies]
dev = [
    "black>=24.2.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ["py312"]
include = '\.pyi?$'
extend-exclude = '''
# A regex preceded with ^/ will apply only to files and directories
# in the root of the project.
^/build/
'''

[dependency-groups]
dev = [
    "black>=25.1.0",
]
